import { system } from "@minecraft/server";
export const entitiesWithMusic = new Map([
    ["ditsh:ao_oni", "mob.ditsh.ao_oni.chase"],
    ["ditsh:armless", "mob.ditsh.armless.chase"],
    ["ditsh:headless_horseman", "mob.ditsh.headless_horseman.chase"],
    ["ditsh:herobrine", "mob.ditsh.herobrine.chase"],
    ["ditsh:jack_black", "mob.ditsh.jack_black.chase"],
    ["ditsh:jeff", "mob.ditsh.jeff.chase"],
    ["ditsh:mac_tonight", "mob.ditsh.mac_tonight.chase"],
    ["ditsh:mama_tattletail", "mob.ditsh.mama_tattletail.chase"],
    ["ditsh:murder_monkey", "mob.ditsh.murder_monkey.chase"],
    ["ditsh:mx", "mob.ditsh.mx.chase"],
    ["ditsh:nun", "mob.ditsh.nun.chase"],
    ["ditsh:rosemary", "mob.ditsh.rosemary.chase"],
    ["ditsh:scp096", "mob.ditsh.scp096.chase"]
]);
export function playMusicForEntity(entity, music) {
    const nearbyPlayers = getNearbyPlayers(entity, 256);
    for (const player of nearbyPlayers) {
        const isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
        if (!isCurrentlyPlaying) {
            player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
            player.setDynamicProperty(`${entity.typeId}_music`, true);
        }
    }
}
export function stopMusicForEntity(entity, music) {
    const nearbyPlayers = getNearbyPlayers(entity, 256);
    for (const player of nearbyPlayers) {
        const isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
        if (isCurrentlyPlaying) {
            player.runCommand(`stopsound @s ${music}`);
            player.setDynamicProperty(`${entity.typeId}_music`, false);
        }
    }
}
export async function continueMusicForEntity(entity, music) {
    const playMusic = entity.getProperty("ditsh:playing_music");
    if (playMusic) {
        await system.waitTicks(140);
        const nearbyPlayers = getNearbyPlayers(entity, 256);
        for (const player of nearbyPlayers) {
            const isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
            if (!isCurrentlyPlaying) {
                player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
                player.setDynamicProperty(`${entity.typeId}_music`, true);
            }
        }
    }
}
export function resetPlayerMusic(player) {
    for (const [entityTypeId, music] of entitiesWithMusic) {
        const isCurrentlyPlaying = isPlayerPlayingMusic(player, entityTypeId);
        if (isCurrentlyPlaying) {
            player.runCommand(`stopsound @s ${music}`);
            player.setDynamicProperty(`${entityTypeId}_music`, false);
        }
    }
}
function getNearbyPlayers(entity, range) {
    const players = entity.dimension.getPlayers({ location: entity.location, maxDistance: range });
    return players;
}
function isPlayerPlayingMusic(player, entityTypeId) {
    const playingMusic = player.getDynamicProperty(`${entityTypeId}_music`);
    return playingMusic ?? false;
}
